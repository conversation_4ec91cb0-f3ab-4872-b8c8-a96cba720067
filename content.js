/**
 * Content script for YouTube Music Live Lyrics Extension
 * Runs on music.youtube.com and provides synchronized lyrics overlay
 *
 * <AUTHOR>
 * @version 1.0.0
 *
 * Features:
 * - Real-time song detection
 * - Synchronized lyrics display
 * - Auto-scrolling with user control
 * - Customizable settings per song
 * - Multi-tab management
 * - Responsive design
 */

// ============================================================================
// CONFIGURATION - Edit these values to customize the extension
// ============================================================================

const CONTENT_CONFIG = {
    // DOM Selectors
    SELECTORS: {
        TITLE: '.content-info-wrapper .title',
        ARTIST: '.content-info-wrapper .subtitle a',
        TIME_INFO: '.time-info',
        TAB_HEADERS: '.tab-header',
        TAB_RENDERER: '#tab-renderer',
        SECTION_LIST: '#tab-renderer > ytmusic-section-list-renderer',
        PLAYER_PAGE: 'ytmusic-player-page',
        VIDEO_ELEMENT: 'video',
        SETTINGS_BUTTON: '.settings-button'
    },

    // CSS Classes
    CSS_CLASSES: {
        LYRICS_WRAPPER: 'ytmusic-lyrics-wrapper',
        LYRICS_CONTENT: 'ytmusic-lyrics-content',
        LYRICS_SOURCE: 'ytmusic-lyrics-source',
        LYRICS_LINE: 'ytmusic-lyrics-line',
        LYRICS_LOADING: 'ytmusic-lyrics-loading',
        LYRICS_ERROR: 'ytmusic-lyrics-error',
        STATIC_LYRICS: 'ytmusic-static-lyrics',
        TAB_CONTENT: '.tab-content',
        SCROLLER: 'scroller'
    },

    // Attributes and Data
    ATTRIBUTES: {
        ARIA_SELECTED: 'aria-selected',
        DATA_LYRICS_ACTIVE: 'data-lyrics-active',
        PAGE_TYPE: 'page-type',
        DATA_TIME: 'data-time',
        DATA_INDEX: 'data-index'
    },

    // Page Types
    PAGE_TYPES: {
        TRACK_LYRICS: 'MUSIC_PAGE_TYPE_TRACK_LYRICS',
        TRACK_RELATED: 'MUSIC_PAGE_TYPE_TRACK_RELATED'
    },

    // Timing Configuration
    TIMING: {
        SONG_CHANGE_DELAY: 1500,        // Delay after song change before fetching lyrics
        DURATION_RETRY_DELAY: 500,      // Delay between duration fetch attempts
        MAX_DURATION_ATTEMPTS: 10,      // Max attempts to get song duration
        SYNC_UPDATE_INTERVAL: 200,      // Lyrics sync update interval (ms)
        USER_SCROLL_TIMEOUT: 1000,      // Time to wait after user scroll before auto-scroll resumes
        PROGRAMMATIC_SCROLL_DELAY: 100, // Delay to clear programmatic scroll flag
        CHROME_MESSAGE_TIMEOUT: 15000,  // Timeout for chrome messaging
        FALLBACK_POLL_INTERVAL: 3000,   // Fallback song detection polling interval
        LYRICS_EARLY_SHOW: 0,         // Show lyrics X seconds early for sync
        SEEK_HIGHLIGHT_DELAY: 100,      // Delay after seek before updating highlight
        TOAST_DURATION: 3000,           // Toast notification duration
        MODAL_FADE_DELAY: 10,           // Modal fade in delay
        MODAL_FADE_DURATION: 300        // Modal fade out duration
    },

    // Error Messages (user-friendly)
    ERROR_MESSAGES: {
        INVALID_SONG_INFO: 'Unable to get song information. Please try playing a different song.',
        PAGE_NOT_LOADED: 'Please refresh the page and try again.',
        EXTENSION_COMMUNICATION: 'Extension communication error. Please refresh the page and try again.',
        LYRICS_TIMEOUT: 'Request timed out. Please check your connection and try again.',
        NETWORK_ERROR: 'Network error. Please check your internet connection.',
        INVALID_LYRICS_DATA: 'Invalid lyrics data received.',
        CONTAINER_CREATION_FAILED: 'Unable to create lyrics display. Please refresh the page.',
        CORRUPTED_SYNCED_LYRICS: 'Synced lyrics data is corrupted.',
        CORRUPTED_STATIC_LYRICS: 'Invalid static lyrics format received.',
        UNKNOWN_LYRICS_FORMAT: 'Unknown lyrics format received.',
        DISPLAY_ERROR: 'Error displaying lyrics. Please try refreshing the page.',
        INITIALIZATION_FAILED: 'Extension failed to initialize properly. Please refresh the page.',
        PROCESSING_ERROR: 'Error processing song change. Please try manually selecting the lyrics tab.',
        UNKNOWN_ERROR: 'An unknown error occurred',
        NO_RESPONSE: 'Unable to get lyrics response. Please try again.'
    },

    // Console Messages
    CONSOLE: {
        EXTENSION_INIT: '🎵 YouTube Music Lyrics Extension initialized',
        INIT_COMPLETED: '🎵 Extension initialization completed successfully',
        SONG_CHANGED: '🎵 Song changed:',
        FETCHING_LYRICS: '🎵 Fetching lyrics for:',
        CACHED_LYRICS: '🎵 Using cached lyrics for:',
        LYRICS_TAB_SELECTED: '🎵 Lyrics tab selected, setting up overlay',
        LYRICS_TAB_NOT_SELECTED: '🎵 Lyrics tab not selected, hiding overlay',
        DISPLAYING_LYRICS: '🎵 Displaying lyrics:',
        DISPLAYING_SYNCED: '🎵 Displaying synced lyrics with',
        DISPLAYING_STATIC: '🎵 Displaying static lyrics',
        STARTING_SYNC: '🎵 Starting lyrics synchronization',
        DURATION_ATTEMPT: '⏱️ Attempting to get song duration...',
        GOT_DURATION: '⏱️ Got valid duration:',
        NO_DURATION: '⏱️ Could not get valid duration after',
        RAW_TIME_INFO: '⏱️ Raw time info:',
        PARSED_DURATION: '⏱️ Parsed total duration:',
        CONTAINER_NOT_FOUND: '🎵 Lyrics container not found, creating...',
        OVERLAY_CREATED: '🎵 Created lyrics overlay and added to tab renderer',
        MESSAGE_LISTENER_SETUP: '🎵 Message listener set up successfully',
        SCROLL_DETECTION: '🎵 User is scrolling manually',
        AUTO_SCROLL_RESUMED: '🎵 Auto-scroll resumed',
        PAUSE_DETECTED: '🎵 Playback paused, stopping auto-scroll',
        RESUME_DETECTED: '🎵 Playback resumed',
        CLEANUP_COMPLETED: '🎵 Extension cleanup completed'
    },

    // UI Text
    UI_TEXT: {
        LOADING_ICON: '🎤',
        LOADING_TEXT: 'Searching for lyrics...',
        LOADING_SUBTEXT: 'This may take a moment',
        ERROR_ICON: '❌',
        SOURCE_PREFIX: 'Source: ',
        SETTINGS_MENU: '⚙️',
        SETTINGS_TITLE: 'Lyrics Settings',
        SYNC_OFFSET_LABEL: 'Sync Offset (seconds)',
        AUTO_SCROLL_LABEL: 'Auto-scroll to current line',
        FONT_SIZE_LABEL: 'Font size',
        HIGHLIGHT_COLOR_LABEL: 'Theme color',
        SAVE_CURRENT_SONG: 'Save for current song',
        SAVE_ALL_SONGS: 'Save for all songs',
        APPLY_TEMPORARY: 'Apply temporarily',
        RESET_DEFAULTS: 'Reset to defaults',
        SETTINGS_APPLIED: 'Settings applied',
        SETTINGS_LOADED: 'Settings loaded for this song'
    },

    // Settings Configuration
    SETTINGS: {
        STORAGE_KEYS: {
            GLOBAL: 'ytmusic_lyrics_global_settings',
            SONG_SPECIFIC: 'ytmusic_lyrics_song_settings'
        },
        DEFAULTS: {
            SYNC_OFFSET: 0,           // seconds to offset lyrics sync
            AUTO_SCROLL: true,        // enable auto-scroll
            FONT_SIZE: 16,           // font size in px
            HIGHLIGHT_COLOR: '#ff0000' // active line highlight color
        },
        LIMITS: {
            SYNC_OFFSET: { min: -10, max: 10, step: 0.1 },
            FONT_SIZE: { min: 12, max: 24, step: 1 }
        }
    }
};

// ============================================================================
// MAIN CLASS
// ============================================================================

/**
 * Main extension class that handles all lyrics functionality
 * @class
 */
class YouTubeMusicLyricsExtension {
    /**
     * Initialize the extension with all required properties
     */
    constructor() {
        // Core state
        this.currentSong = null;
        this.lyricsContainer = null;
        this.lyricsWrapper = null;
        this.isEnabled = true;
        this.lyricsData = null;

        // Synchronization state
        this.syncInterval = null;
        this.lastActiveIndex = -1;

        // UI state
        this.lyricsTabRenderer = null;
        this.hadScrollerClass = false;
        this.isLyricsTabActive = false;
        this.lyricsSourceContainer = null;

        // User interaction state
        this.isUserScrolling = false;
        this.userScrollTimeout = null;
        this.programmaticScroll = false;

        // Performance optimizations
        this.lyricsCache = new Map();
        this.cachedElements = {};
        this.pendingTabUpdate = null;

        // Logging throttling
        this.lastScrollLog = 0;

        // Observers
        this.tabObserver = null;
        this.sectionListObserver = null;
        this.pageTypeObserver = null;
        this.lyricsTabObserver = null;

        // Video event listeners
        this.videoEventListeners = new Map();

        // Settings system
        this.currentSettings = { ...CONTENT_CONFIG.SETTINGS.DEFAULTS };
        this.settingsModal = null;
        this.originalSettings = null;
        this.colorPickerDebounceTimeout = null;

        this.initializeExtension();
    }

    /**
     * Initialize the extension
     * @private
     */
    initializeExtension() {
        this.loadSettings();
        this.init();
    }

    /**
     * Initialize the extension with error handling
     * @private
     */
    init() {
        try {
            console.log(CONTENT_CONFIG.CONSOLE.EXTENSION_INIT);

            if (!this.validateEnvironment()) {
                return;
            }

            this.setupExtensionComponents();

            console.log(CONTENT_CONFIG.CONSOLE.INIT_COMPLETED);
        } catch (error) {
            this.handleInitializationError(error);
        }
    }

    /**
     * Validate that we're running in the correct environment
     * @returns {boolean} True if environment is valid
     * @private
     */
    validateEnvironment() {
        // Check if we're on the correct page
        if (!window.location.hostname.includes('music.youtube.com')) {
            console.log('❌ Not on YouTube Music domain, extension will not function');
            return false;
        }

        // Check if essential APIs are available
        if (!chrome || !chrome.runtime) {
            console.error('❌ Chrome extension APIs not available');
            return false;
        }

        return true;
    }

    /**
     * Set up all extension components
     * @private
     */
    setupExtensionComponents() {
        this.observeSongChanges();
        this.watchTabChanges();
        this.setupSectionListObserver();
        this.setupLyricsTabObserver();
        this.setupVideoEventListeners();
        this.setupMessageListener();
    }

    /**
     * Handle initialization errors gracefully
     * @param {Error} error - The initialization error
     * @private
     */
    handleInitializationError(error) {
        console.error('❌ Critical error during extension initialization:', error);

        // Try to show a minimal error message if possible
        setTimeout(() => {
            try {
                if (this.lyricsContainer || document.querySelector('#tab-renderer')) {
                    this.showError(CONTENT_CONFIG.ERROR_MESSAGES.INITIALIZATION_FAILED);
                }
            } catch (displayError) {
                console.error('❌ Could not display initialization error:', displayError);
            }
        }, 2000);
    }

    /**
     * Detect current song information from the page
     * @returns {Object|null} Song information object or null if not available
     */
    getCurrentSongInfo() {
        try {
            const elements = this.getSongInfoElements();
            if (!elements) {
                return null;
            }

            const songInfo = this.extractSongInfo(elements);
            if (!this.validateExtractedSongInfo(songInfo)) {
                return null;
            }

            return songInfo;
        } catch (error) {
            console.error('❌ Error getting current song info:', error);
            return null;
        }
    }

    /**
     * Get song information DOM elements
     * @returns {Object|null} Elements object or null if not found
     * @private
     */
    getSongInfoElements() {
        const titleElement = document.querySelector(CONTENT_CONFIG.SELECTORS.TITLE);
        const artistElement = document.querySelector(CONTENT_CONFIG.SELECTORS.ARTIST);

        if (!titleElement || !artistElement) {
            console.log('🎵 Song info elements not found - page may still be loading');
            return null;
        }

        return { titleElement, artistElement };
    }

    /**
     * Extract song information from DOM elements
     * @param {Object} elements - DOM elements containing song info
     * @returns {Object} Song information object
     * @private
     */
    extractSongInfo(elements) {
        const title = elements.titleElement.textContent?.trim() || '';
        const artist = elements.artistElement.textContent?.trim() || '';

        return { title, artist };
    }

    /**
     * Validate extracted song information
     * @param {Object} songInfo - Song information to validate
     * @returns {boolean} True if valid, false otherwise
     * @private
     */
    validateExtractedSongInfo(songInfo) {
        const { title, artist } = songInfo;

        // Check for empty content
        if (!title || !artist || title.length === 0 || artist.length === 0) {
            console.log('🎵 Song info is empty - no song playing or page loading');
            return false;
        }

        // Check for placeholder text that indicates loading state
        const loadingIndicators = ['Loading', '...'];
        if (loadingIndicators.some(indicator =>
            title.includes(indicator) || artist.includes(indicator))) {
            console.log('🎵 Song info shows loading state');
            return false;
        }

        return true;
    }

    /**
     * Get current song duration in seconds
     * @returns {number|null} Duration in seconds or null if not available
     */
    /**
     * Get current song duration in seconds
     * @returns {number|null} Duration in seconds or null if not available
     */
    getCurrentSongDuration() {
        try {
            // Try to get duration from UI display first
            const timeInfo = this.getTimeInfo();
            if (timeInfo) {
                const duration = this.parseDurationFromTimeInfo(timeInfo);
                if (duration !== null) {
                    console.log(CONTENT_CONFIG.CONSOLE.PARSED_DURATION, duration, 'seconds');
                    return duration;
                }
            }

            // Fallback: Try video element duration
            const videoElement = document.querySelector(CONTENT_CONFIG.SELECTORS.VIDEO_ELEMENT);
            if (videoElement && !isNaN(videoElement.duration) && videoElement.duration > 0) {
                return videoElement.duration;
            }

            return null;
        } catch (error) {
            console.error('⏱️ Error getting song duration:', error);
            return null;
        }
    }

    /**
     * Get time information from the page
     * @returns {string|null} Time info text or null if not available
     * @private
     */
    getTimeInfo() {
        const timeInfoElement = document.querySelector(CONTENT_CONFIG.SELECTORS.TIME_INFO);
        if (!timeInfoElement) {
            console.log('⏱️ No .time-info element found');
            return null;
        }

        const timeText = timeInfoElement.textContent?.trim();
        console.log(CONTENT_CONFIG.CONSOLE.RAW_TIME_INFO, timeText);

        if (!timeText) {
            console.log('⏱️ Time info is empty, song may be loading');
            return null;
        }

        return timeText;
    }

    /**
     * Parse duration from time info text
     * @param {string} timeText - Time info text (e.g., "1:23 / 3:45")
     * @returns {number|null} Duration in seconds or null if invalid
     * @private
     */
    parseDurationFromTimeInfo(timeText) {
        // Expected format: "0:31 / 3:27" or "1:23:45 / 2:40:23"
        const parts = timeText.split(' / ');
        if (parts.length !== 2) {
            console.log('⏱️ Unexpected time format:', timeText);
            return null;
        }

        const totalTimeStr = parts[1].trim();

        // Handle loading state where duration shows as "0:00"
        if (totalTimeStr === '0:00') {
            console.log('⏱️ Duration shows 0:00, song may still be loading');
            return null;
        }

        return this.parseTimeToSeconds(totalTimeStr);
    }

    /**
     * Get current song duration with retry logic for loading songs
     * @param {number} maxAttempts - Maximum number of retry attempts
     * @param {number} delayMs - Delay between attempts in milliseconds
     * @returns {Promise<number|null>} Duration in seconds or null if failed
     */
    async getCurrentSongDurationWithRetry(
        maxAttempts = CONTENT_CONFIG.TIMING.MAX_DURATION_ATTEMPTS,
        delayMs = CONTENT_CONFIG.TIMING.DURATION_RETRY_DELAY
    ) {
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            console.log(CONTENT_CONFIG.CONSOLE.DURATION_ATTEMPT, `(${attempt}/${maxAttempts})`);

            const duration = this.getCurrentSongDuration();

            if (duration && duration > 0) {
                console.log(CONTENT_CONFIG.CONSOLE.GOT_DURATION, `${duration}s on attempt ${attempt}`);
                return duration;
            }

            if (attempt < maxAttempts) {
                await this.delay(delayMs);
            }
        }

        console.log(CONTENT_CONFIG.CONSOLE.NO_DURATION, maxAttempts, 'attempts');
        return null;
    }

    /**
     * Utility method to create a delay
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise<void>} Promise that resolves after delay
     * @private
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Convert time string to seconds (supports mm:ss and h:mm:ss formats)
     * @param {string} timeStr - Time string to parse
     * @returns {number|null} Time in seconds or null if invalid
     */
    parseTimeToSeconds(timeStr) {
        if (!timeStr || typeof timeStr !== 'string') {
            return null;
        }

        const parts = timeStr.split(':').map(part => parseInt(part.trim(), 10));

        if (parts.some(part => isNaN(part) || part < 0)) {
            console.log('⏱️ Invalid time parts:', timeStr);
            return null;
        }

        let seconds = 0;

        if (parts.length === 2) {
            // mm:ss format
            const [minutes, secs] = parts;
            seconds = minutes * 60 + secs;
        } else if (parts.length === 3) {
            // h:mm:ss or hh:mm:ss format
            const [hours, minutes, secs] = parts;
            seconds = hours * 3600 + minutes * 60 + secs;
        } else {
            console.log('⏱️ Unexpected time format:', timeStr);
            return null;
        }

        return seconds > 0 ? seconds : null;
    }

    /**
     * Format time in seconds to MM:SS format
     * @param {number} seconds - Time in seconds
     * @returns {string} Formatted time string
     * @private
     */
    formatTime(seconds) {
        if (isNaN(seconds) || seconds < 0) return '0:00';

        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);

        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        } else if (minutes > 0) {
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        } else {
            return `0:${secs.toString().padStart(2, '0')}`;
        }
    }



    // Find and setup the lyrics tab renderer
    findLyricsTab() {
        const tabHeaders = document.querySelectorAll('.tab-header');
        console.log('🎵 Found tab headers:', tabHeaders.length);

        if (tabHeaders.length >= 3) {
            // The second element (index 1) is the lyrics tab
            const lyricsTabHeader = tabHeaders[1];
            const isSelected = lyricsTabHeader.getAttribute('aria-selected') === 'true';

            console.log('🎵 Lyrics tab header aria-selected:', isSelected);

            if (isSelected) {
                // Find the tab renderer element
                const tabRenderer = document.querySelector('#tab-renderer');

                if (tabRenderer) {
                    console.log('🎵 Found tab renderer and lyrics tab is selected');
                    this.lyricsTabRenderer = tabRenderer;
                    return true;
                }
            }
        }

        console.log('🎵 Lyrics tab not selected or not found');
        return false;
    }

    // Watch for tab changes using mutation observer
    watchTabChanges() {
        this.tabObserver = new MutationObserver((mutations) => {
            // Check if we're dealing with tab changes to avoid infinite loops
            let shouldProcess = false;
            for (const mutation of mutations) {
                if (mutation.type === 'attributes' &&
                    mutation.attributeName === 'aria-selected' &&
                    mutation.target.classList.contains('tab-header')) {
                    shouldProcess = true;
                    break;
                }
            }

            if (!shouldProcess) { return; }

            const tabHeaders = document.querySelectorAll('.tab-header');
            if (tabHeaders.length >= 3) {
                const lyricsTabHeader = tabHeaders[1];
                const isSelected = lyricsTabHeader.getAttribute('aria-selected') === 'true';

                if (isSelected !== this.isLyricsTabActive) {
                    this.isLyricsTabActive = isSelected;

                    // Debounce tab updates to prevent lag
                    if (this.pendingTabUpdate) {
                        cancelAnimationFrame(this.pendingTabUpdate);
                    }

                    this.pendingTabUpdate = requestAnimationFrame(() => {
                        if (isSelected) {
                            // Lyrics tab is selected
                            console.log('🎵 Lyrics tab selected, setting up overlay');
                            this.setupLyricsOverlay();
                            this.manageSectionListVisibility(false); // Hide section list

                            // If we have a current song, fetch lyrics for it (check cache first)
                            if (this.currentSong) {
                                console.log('🎵 Checking cache and fetching lyrics for current song');
                                this.fetchLyricsWithCache(this.currentSong);

                                // Immediately update highlight for current playback position
                                this.updateHighlightOnTabSwitch();
                            }
                        } else {
                            // Lyrics tab is not selected
                            console.log('🎵 Lyrics tab not selected, hiding overlay');
                            this.hideLyricsOverlay();
                            this.manageSectionListVisibility(true); // Show section list
                        }
                        this.pendingTabUpdate = null;
                    });
                }
            }
        });

        // Observe changes to tab headers and their attributes
        const tabContainer = document.querySelector('ytmusic-player-page');
        if (tabContainer) {
            this.tabObserver.observe(tabContainer, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['aria-selected']
            });
        }
    }

    // Manage section list visibility with enforced observer
    manageSectionListVisibility(show) {
        // Use cached element or query if not cached
        let { sectionList } = this.cachedElements;
        if (!sectionList || !sectionList.isConnected) {
            sectionList = document.querySelector('#tab-renderer > ytmusic-section-list-renderer');
            this.cachedElements.sectionList = sectionList;
        }

        if (sectionList) {
            const pageType = sectionList.getAttribute('page-type');
            console.log('🎵 Section list page-type:', pageType);

            // Check page type before making any changes
            if (pageType === 'MUSIC_PAGE_TYPE_TRACK_RELATED') {
                // For related tracks page, don't change anything regardless of current state
                console.log('🎵 Track related page detected - no changes will be made');
                return;
            }

            // Only proceed with changes for lyrics page
            if (pageType !== 'MUSIC_PAGE_TYPE_TRACK_LYRICS') {
                console.log('🎵 Not a track lyrics page - no changes will be made');
                return;
            }

            if (show) {
                // Stop enforcing the hide
                this.stopSectionListEnforcement();
                // Remove all our styling
                this.removeSectionListStyling(sectionList);
                console.log('🎵 Showed section list');
            } else {
                // Hide using multiple techniques and start enforcing
                this.forceHideSectionList();
                this.startSectionListEnforcement(sectionList);
                console.log('🎵 Hid section list and started enforcement');
            }
        }
    }

    // Start observing section list to enforce display: none
    startSectionListEnforcement(sectionList) {
        // Stop any existing observer first
        this.stopSectionListEnforcement();

        this.sectionListObserver = new MutationObserver((mutations) => {
            // Only process if lyrics tab is active
            if (!this.isLyricsTabActive) { return; }

            // Check page type before enforcing
            const currentPageType = sectionList.getAttribute('page-type');

            if (currentPageType === 'MUSIC_PAGE_TYPE_TRACK_RELATED') {
                // For related tracks page, don't enforce anything
                console.log('🎵 Track related page detected - stopping enforcement');
                return;
            }

            if (currentPageType !== 'MUSIC_PAGE_TYPE_TRACK_LYRICS') {
                // Only enforce for lyrics pages
                console.log('🎵 Not a track lyrics page - stopping enforcement');
                return;
            }

            let needsEnforcement = false;

            for (const mutation of mutations) {
                // Check if style attribute was modified
                if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                    const currentDisplay = sectionList.style.display;
                    const currentVisibility = sectionList.style.visibility;
                    if (currentDisplay !== 'none' || currentVisibility !== 'hidden') {
                        needsEnforcement = true;
                        break;
                    }
                }
                // Check if the element was removed and re-added
                else if (mutation.type === 'childList') {
                    // Re-query the section list in case it was replaced
                    const newSectionList = document.querySelector('#tab-renderer > ytmusic-section-list-renderer');
                    if (newSectionList && newSectionList !== sectionList) {
                        needsEnforcement = true;
                        break;
                    }
                }
            }

            if (needsEnforcement) {
                // Re-enforce all hiding techniques
                this.forceHideSectionList();
                console.log('🎵 Enforced section list hiding (was being overridden)');
            }
        });

        // Observe both attribute changes and parent node changes
        this.sectionListObserver.observe(sectionList, {
            attributes: true,
            attributeFilter: ['style', 'class', 'aria-hidden', 'page-type']
        });

        // Also observe the parent in case the element gets replaced
        const tabRenderer = sectionList.parentNode;
        if (tabRenderer) {
            this.sectionListObserver.observe(tabRenderer, {
                childList: true,
                subtree: true
            });
        }

        console.log('🎵 Started section list enforcement observer');
    }

    // Stop section list enforcement observer
    stopSectionListEnforcement() {
        if (this.sectionListObserver) {
            this.sectionListObserver.disconnect();
            this.sectionListObserver = null;
            console.log('🎵 Stopped section list enforcement observer');
        }
    }

    // Setup general section list observer to track changes
    setupSectionListObserver() {
        let observerSetup = false;

        // Wait for DOM to be ready and check periodically for the section list element
        const checkForSectionList = () => {
            if (observerSetup) { return; } // Prevent duplicate setup

            const sectionList = document.querySelector('#tab-renderer > ytmusic-section-list-renderer');
            if (sectionList) {
                this.observeSectionListChanges(sectionList);
                observerSetup = true;
                console.log('🎵 Found section list element and set up observer');
            } else {
                // Check again after a delay, but with exponential backoff to reduce CPU usage
                setTimeout(checkForSectionList, observerSetup ? 5000 : 1000);
            }
        };

        // Start checking
        checkForSectionList();

        // Also observe the tab-renderer for when section list gets added/removed
        const tabRenderer = document.querySelector('#tab-renderer');
        if (tabRenderer) {
            const tabObserver = new MutationObserver((mutations) => {
                if (observerSetup) { return; } // Don't setup multiple observers

                for (const mutation of mutations) {
                    if (mutation.type === 'childList') {
                        const addedNodes = Array.from(mutation.addedNodes);
                        const sectionListAdded = addedNodes.find(node =>
                            node.nodeName === 'YTMUSIC-SECTION-LIST-RENDERER'
                        );
                        if (sectionListAdded) {
                            this.observeSectionListChanges(sectionListAdded);
                            observerSetup = true;
                            console.log('🎵 New section list element detected and observer set up');
                        }
                    }
                }
            });

            tabObserver.observe(tabRenderer, {
                childList: true,
                subtree: true
            });
        }
    }

    // Observe changes to the section list element
    observeSectionListChanges(sectionList) {
        // Prevent duplicate observers
        if (this.pageTypeObserver) {
            this.pageTypeObserver.disconnect();
        }

        this.pageTypeObserver = new MutationObserver((mutations) => {
            // Debounce mutations to avoid rapid-fire operations
            setTimeout(() => {
                for (const mutation of mutations) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'page-type') {
                        const newPageType = sectionList.getAttribute('page-type');
                        console.log('🎵 Section list page-type changed to:', newPageType);

                        // If page type changed to related tracks, remove any styling we might have applied
                        if (newPageType === 'MUSIC_PAGE_TYPE_TRACK_RELATED') {
                            // Remove our extension's styling to let YouTube Music control it
                            this.removeSectionListStyling(sectionList);
                            console.log('🎵 Removed extension styling for related tracks page');
                        }
                    }
                }
            }, 0); // Use setTimeout to defer to next tick
        });

        this.pageTypeObserver.observe(sectionList, {
            attributes: true,
            attributeFilter: ['page-type']
        });

        console.log('🎵 Set up page-type observer for section list');
    }

    // Setup lyrics overlay
    setupLyricsOverlay() {
        const tabRenderer = document.querySelector('#tab-renderer');
        if (!tabRenderer) {
            console.log('🎵 Tab renderer not found');
            return;
        }

        this.lyricsTabRenderer = tabRenderer;

        // Set data attribute for CSS targeting
        tabRenderer.setAttribute('data-lyrics-active', 'true');

        // Handle scroller class
        this.hadScrollerClass = tabRenderer.classList.contains('scroller');
        if (this.hadScrollerClass) {
            tabRenderer.classList.remove('scroller');
            console.log('🎵 Removed scroller class from tab renderer');
        }

        // Create or show lyrics container if it doesn't exist
        if (!this.lyricsWrapper) {
            this.createLyricsOverlay();
        } else {
            this.lyricsWrapper.style.display = 'flex';
            console.log('🎵 Showed existing lyrics overlay');
        }
    }

    // Hide lyrics overlay
    hideLyricsOverlay() {
        // Batch DOM operations to avoid forced reflow
        requestAnimationFrame(() => {
            if (this.lyricsWrapper) {
                this.lyricsWrapper.style.display = 'none';
                this.stopSynchronization();
                console.log('🎵 Hid lyrics overlay');
            }

            // Remove data attribute for CSS targeting
            if (this.lyricsTabRenderer) {
                this.lyricsTabRenderer.removeAttribute('data-lyrics-active');
            }

            // Restore scroller class if it existed
            if (this.lyricsTabRenderer && this.hadScrollerClass) {
                this.lyricsTabRenderer.classList.add('scroller');
                console.log('🎵 Restored scroller class to tab renderer');
            }
        });
    }

    // Create lyrics overlay as a child element
    createLyricsOverlay() {
        const tabRenderer = document.querySelector('#tab-renderer');
        if (!tabRenderer) {
            console.log('🎵 Tab renderer not found, cannot create overlay');
            return;
        }

        // Create wrapper container
        this.lyricsWrapper = document.createElement('div');
        this.lyricsWrapper.className = 'ytmusic-lyrics-wrapper';

        // Create lyrics content container
        this.lyricsContainer = document.createElement('div');
        this.lyricsContainer.className = 'ytmusic-lyrics-content';

        // Create source container
        this.lyricsSourceContainer = document.createElement('div');
        this.lyricsSourceContainer.className = 'ytmusic-lyrics-source';

        // Set up initial content
        this.lyricsContainer.innerHTML = `
            <div class="ytmusic-lyrics-loading">🎵 Searching for lyrics...</div>
        `;

        // Append to wrapper
        this.lyricsWrapper.appendChild(this.lyricsContainer);
        this.lyricsWrapper.appendChild(this.lyricsSourceContainer);

        // Add overlay to tab renderer
        tabRenderer.appendChild(this.lyricsWrapper);

        // Add scroll event listener for user scroll detection
        this.setupScrollListeners();

        console.log('🎵 Created lyrics overlay and added to tab renderer');
    }

    // Generate cache key for lyrics
    getCacheKey(songInfo) {
        return `${songInfo.artist}-${songInfo.title}`.toLowerCase().replace(/[^a-z0-9]/g, '');
    }

    // Fetch lyrics with caching
    async fetchLyricsWithCache(songInfo) {
        const cacheKey = this.getCacheKey(songInfo);

        // Check cache first
        if (this.lyricsCache.has(cacheKey)) {
            console.log('🎵 Using cached lyrics for:', songInfo.title);
            const cachedData = this.lyricsCache.get(cacheKey);
            this.displayLyrics(cachedData);
            this.startSynchronization();
            return;
        }

        // Not in cache, fetch from API
        console.log('🎵 Fetching lyrics from API for:', songInfo.title);
        await this.fetchLyrics(songInfo);
    }


    // Observe for song changes
    observeSongChanges() {
        try {
            const observer = new MutationObserver(() => {
                try {
                    const songInfo = this.getCurrentSongInfo();
                    if (songInfo && JSON.stringify(songInfo) !== JSON.stringify(this.currentSong)) {
                        console.log('🎵 Song changed:', songInfo);
                        this.currentSong = songInfo;

                        // Load settings for the new song
                        this.loadSettings(songInfo);

                        // Always try to fetch lyrics for new songs (with caching)
                        setTimeout(() => {
                            try {
                                console.log('🎵 Attempting to fetch lyrics for song change');
                                this.fetchLyricsWithCache(songInfo);
                            } catch (error) {
                                console.error('❌ Error in delayed lyrics fetch:', error);
                                this.showError('Error processing song change. Please try manually selecting the lyrics tab.');
                            }
                        }, 1500); // Delay to let YouTube Music load completely
                    }
                } catch (error) {
                    console.error('❌ Error in song change observer:', error);
                    // Don't show error to user for observer errors, just log them
                }
            });

            const bodyElement = document.body;
            if (!bodyElement) {
                console.error('❌ Document body not found, cannot observe song changes');
                return;
            }

            observer.observe(bodyElement, {
                childList: true,
                subtree: true
            });

            console.log('🎵 Song change observer set up successfully');
        } catch (error) {
            console.error('❌ Failed to set up song change observer:', error);
            // Try to set up a fallback polling mechanism
            this.setupFallbackSongDetection();
        }
    }

    // Fallback mechanism for song detection if MutationObserver fails
    setupFallbackSongDetection() {
        console.log('🎵 Setting up fallback song detection via polling');

        setInterval(() => {
            try {
                const songInfo = this.getCurrentSongInfo();
                if (songInfo && JSON.stringify(songInfo) !== JSON.stringify(this.currentSong)) {
                    console.log('🎵 Song changed (via polling):', songInfo);
                    this.currentSong = songInfo;

                    setTimeout(() => {
                        this.fetchLyricsWithCache(songInfo);
                    }, 1500);
                }
            } catch (error) {
                console.error('❌ Error in fallback song detection:', error);
            }
        }, 3000); // Check every 3 seconds
    }

    /**
     * Set up video element event listeners for seeking detection
     * @private
     */
    setupVideoEventListeners() {
        try {
            const videoElement = document.querySelector(CONTENT_CONFIG.SELECTORS.VIDEO_ELEMENT);
            if (!videoElement) {
                console.log('🎵 Video element not found yet, will retry when lyrics are displayed');
                return;
            }

            // Remove existing listeners first to prevent duplicates
            this.removeVideoEventListeners();

            // Add seeked event listener to detect manual time changes (slider moves)
            const seekedHandler = () => {
                console.log('🎵 Video seeked detected, updating lyrics highlight');
                this.handleVideoSeeked();
            };

            // Add timeupdate event listener as backup for any time changes
            const timeupdateHandler = () => {
                // Only handle timeupdate if we're not already syncing (to avoid conflicts)
                if (!this.syncInterval && this.lyricsData && this.lyricsData.type === 'synced') {
                    const currentTime = this.getCurrentPlaybackTime();
                    if (currentTime !== null) {
                        this.forceHighlightUpdate(currentTime);
                    }
                }
            };

            videoElement.addEventListener('seeked', seekedHandler);
            videoElement.addEventListener('timeupdate', timeupdateHandler);

            // Store references for cleanup
            this.videoEventListeners.set('seeked', { element: videoElement, handler: seekedHandler });
            this.videoEventListeners.set('timeupdate', { element: videoElement, handler: timeupdateHandler });

            console.log('🎵 Video event listeners set up successfully');
        } catch (error) {
            console.error('❌ Error setting up video event listeners:', error);
        }
    }

    /**
     * Remove video element event listeners
     * @private
     */
    removeVideoEventListeners() {
        try {
            this.videoEventListeners.forEach((listener, eventType) => {
                if (listener.element && listener.handler) {
                    listener.element.removeEventListener(eventType, listener.handler);
                }
            });
            this.videoEventListeners.clear();
            console.log('🎵 Video event listeners removed');
        } catch (error) {
            console.error('❌ Error removing video event listeners:', error);
        }
    }

    /**
     * Handle video seeked event
     * @private
     */
    handleVideoSeeked() {
        try {
            // Get current time and force highlight update regardless of play state
            const currentTime = this.getCurrentPlaybackTime();
            if (currentTime !== null && this.lyricsData && this.lyricsData.type === 'synced') {
                this.forceHighlightUpdate(currentTime);
                console.log(`🎵 Forced highlight update after seek to ${currentTime.toFixed(1)}s`);
            }
        } catch (error) {
            console.error('❌ Error handling video seeked:', error);
        }
    }

    /**
     * Update highlight when switching back to lyrics tab
     * @private
     */
    updateHighlightOnTabSwitch() {
        try {
            // Small delay to ensure lyrics are loaded and displayed
            setTimeout(() => {
                const currentTime = this.getCurrentPlaybackTime();
                if (currentTime !== null && this.lyricsData && this.lyricsData.type === 'synced') {
                    this.forceHighlightUpdate(currentTime);
                    console.log(`🎵 Updated highlight on tab switch to ${currentTime.toFixed(1)}s`);
                }

                // Also ensure video event listeners are set up
                this.setupVideoEventListeners();
            }, 100); // Small delay to ensure DOM is ready
        } catch (error) {
            console.error('❌ Error updating highlight on tab switch:', error);
        }
    }

    // Setup observer to watch for lyrics tab being disabled by YouTube Music
    setupLyricsTabObserver() {
        const findLyricsTab = () => {
            console.log('🎵 Looking for lyrics tab...');

            // Find all tab headers
            const tabHeaders = document.querySelectorAll('.tab-header');
            console.log(`🎵 Found ${tabHeaders.length} tab headers`);

            if (tabHeaders.length === 0) {
                console.log('🎵 No tab headers found yet');
                return null;
            }

            // Look for the lyrics tab by checking tab content
            for (let i = 0; i < tabHeaders.length; i++) {
                const tabHeader = tabHeaders[i];
                const tabContent = tabHeader.querySelector('.tab-content');

                if (tabContent) {
                    const { textContent } = tabContent;
                    console.log(`🎵 Tab ${i} content:`, JSON.stringify(textContent));

                    // Check if this is the lyrics tab
                    if (textContent.includes('Lyrics')) {
                        console.log(`🎵 Found lyrics tab at index ${i}`);
                        return tabHeader;
                    }
                } else {
                    console.log(`🎵 Tab ${i} has no .tab-content element`);
                }
            }

            console.log('🎵 No lyrics tab found in available tab headers');
            return null;
        };

        const setupObserver = () => {
            const lyricsTab = findLyricsTab();

            if (lyricsTab) {
                console.log('🎵 Setting up observer for lyrics tab');

                // Disconnect existing observer if any
                if (this.lyricsTabObserver) {
                    this.lyricsTabObserver.disconnect();
                }

                this.lyricsTabObserver = new MutationObserver(() => {
                    console.log('🎵 Lyrics tab attributes changed, ensuring it stays enabled');

                    // Simply ensure the tab is always enabled on any change
                    lyricsTab.removeAttribute('disabled');
                    lyricsTab.removeAttribute('aria-disabled');
                    lyricsTab.style.removeProperty('pointer-events');

                    console.log('🎵 Ensured lyrics tab is enabled');
                });

                // Observe all attribute changes to the lyrics tab
                this.lyricsTabObserver.observe(lyricsTab, {
                    attributes: true
                });

                // Also do an initial enable
                const wasDisabled = lyricsTab.hasAttribute('disabled') ||
                    lyricsTab.hasAttribute('aria-disabled') ||
                    lyricsTab.style.pointerEvents === 'none';

                lyricsTab.removeAttribute('disabled');
                lyricsTab.removeAttribute('aria-disabled');
                lyricsTab.style.removeProperty('pointer-events');

                if (wasDisabled) {
                    console.log('🎵 Lyrics tab was disabled, enabled it');
                } else {
                    console.log('🎵 Lyrics tab was already enabled');
                }

                console.log('🎵 Set up lyrics tab observer - tab will stay enabled');
                return true;
            } else {
                console.log('🎵 Could not find lyrics tab to observe');
                return false;
            }
        };

        let attemptCount = 0;
        const maxAttempts = 10;

        const trySetup = () => {
            attemptCount++;
            console.log(`🎵 Attempt ${attemptCount}/${maxAttempts} to set up lyrics tab observer`);

            if (setupObserver()) {
                console.log('🎵 Successfully set up lyrics tab observer');
                return;
            }

            if (attemptCount < maxAttempts) {
                console.log(`🎵 Failed to find lyrics tab, trying again in 1 second...`);
                setTimeout(trySetup, 1000);
            } else {
                console.warn('🎵 WARNING: Could not find lyrics tab after multiple attempts. Extension may not work properly.');
            }
        };

        // Start trying to set up the observer
        trySetup();
    }

    // Fetch lyrics from API
    async fetchLyrics(songInfo) {
        try {
            console.log('🎵 Fetching lyrics for:', songInfo);

            // Check if lyrics tab is selected before fetching
            if (!this.isLyricsTabActive) {
                console.log('🎵 Lyrics tab not selected, skipping fetch');
                return;
            }

            // Validate song information before proceeding
            if (!songInfo || !songInfo.title || !songInfo.artist) {
                console.log('❌ Invalid song information');
                this.showError('Unable to get song information. Please try playing a different song.');
                return;
            }

            // Check if the page is still loaded and functional
            if (!document.querySelector('ytmusic-player-page')) {
                console.log('❌ YouTube Music page not detected');
                this.showError('Please refresh the page and try again.');
                return;
            }

            this.showLoading();

            // Get song duration for better matching (with retry logic)
            console.log('⏱️ Attempting to get song duration...');
            const duration = await this.getCurrentSongDurationWithRetry();

            console.log('🎵 Sending lyrics request to background script');

            // Add timeout for the chrome messaging
            const messagePromise = new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('Extension communication timed out'));
                }, 15000); // 15 second timeout for the entire process

                chrome.runtime.sendMessage({
                    action: 'fetchLyrics',
                    song: {
                        ...songInfo,
                        duration // Add duration to song info
                    }
                }, (response) => {
                    clearTimeout(timeout);

                    if (chrome.runtime.lastError) {
                        reject(new Error(`Extension error: ${chrome.runtime.lastError.message}`));
                        return;
                    }

                    resolve(response);
                });
            });

            const response = await messagePromise;
            console.log('Received lyrics response:', response);

            if (response && response.lyrics) {
                // Cache the lyrics data
                const cacheKey = this.getCacheKey(songInfo);
                this.lyricsCache.set(cacheKey, response.lyrics);
                console.log('🎵 Cached lyrics for:', songInfo.title);

                this.displayLyrics(response.lyrics);
                this.startSynchronization();
            } else if (response && response.error) {
                this.showError(response.error);
            } else {
                this.showError('Unable to get lyrics response. Please try again.');
            }

        } catch (error) {
            console.error('Error fetching lyrics:', error);

            // Provide user-friendly error messages
            let userMessage = 'Unable to fetch lyrics at the moment.';

            if (error.message.includes('Extension error') || error.message.includes('runtime')) {
                userMessage = 'Extension communication error. Please refresh the page and try again.';
            } else if (error.message.includes('timeout') || error.message.includes('timed out')) {
                userMessage = 'Request timed out. Please check your connection and try again.';
            } else if (error.message.includes('network') || error.message.includes('NetworkError')) {
                userMessage = 'Network error. Please check your internet connection.';
            }

            this.showError(userMessage);
        }
    }

    // Display lyrics in the lyrics tab
    displayLyrics(lyricsData) {
        try {
            console.log('🎵 Displaying lyrics:', lyricsData);

            // Validate lyrics data
            if (!lyricsData) {
                console.log('❌ No lyrics data provided');
                this.showError('Invalid lyrics data received.');
                return;
            }

            this.lyricsData = lyricsData;

            if (!this.lyricsContainer) {
                console.log('🎵 Lyrics container not found, creating...');
                this.setupLyricsOverlay();
                if (!this.lyricsContainer) {
                    console.log('❌ Could not create lyrics container');
                    this.showError('Unable to create lyrics display. Please refresh the page.');
                    return;
                }
            }

            // Get the source for footer
            const source = lyricsData.source || 'Unknown';
            const sourceText = `Source: ${source}`;

            if (lyricsData.type === 'synced') {
                if (!lyricsData.lines || !Array.isArray(lyricsData.lines) || lyricsData.lines.length === 0) {
                    console.log('❌ Invalid synced lyrics data');
                    this.showError('Invalid synced lyrics format received.');
                    return;
                }

                console.log('🎵 Displaying synced lyrics with', lyricsData.lines.length, 'lines');

                // Display synchronized lyrics with error handling for malformed lines
                const validLines = lyricsData.lines.filter(line =>
                    line && typeof line.time === 'number' && typeof line.text === 'string'
                );

                if (validLines.length === 0) {
                    console.log('❌ No valid synced lyrics lines found');
                    this.showError('Synced lyrics data is corrupted.');
                    return;
                }

                this.lyricsContainer.innerHTML = validLines.map((line, index) =>
                    `<div class="ytmusic-lyrics-line" data-time="${line.time}" data-index="${index}" title="Go to ${this.formatTime(line.time)}">
                        ${this.escapeHtml(line.text)}
                    </div>`
                ).join('');

                // Add click-to-seek functionality for synced lyrics
                this.setupClickToSeek();

                // Set up video event listeners for seeking detection
                this.setupVideoEventListeners();

                // Update source separately with settings button for synced lyrics
                this.lyricsSourceContainer.innerHTML = `
                    <button class="settings-button" title="Lyrics Settings">
                        ${CONTENT_CONFIG.UI_TEXT.SETTINGS_MENU} Settings
                    </button>
                    <span>${sourceText}</span>
                `;

                // Add settings button click listener
                const settingsButton = this.lyricsSourceContainer.querySelector(CONTENT_CONFIG.SELECTORS.SETTINGS_BUTTON);
                if (settingsButton) {
                    settingsButton.addEventListener('click', () => {
                        this.showSettingsModal();
                    });
                }

                // Start synchronization for synced lyrics
                console.log('🎵 Starting lyrics synchronization');
                this.startSynchronization();

            } else if (lyricsData.type === 'static') {
                if (!lyricsData.text || typeof lyricsData.text !== 'string') {
                    console.log('❌ Invalid static lyrics data');
                    this.showError('Invalid static lyrics format received.');
                    return;
                }

                console.log('🎵 Displaying static lyrics');
                // Display static lyrics with HTML escaping
                this.lyricsContainer.innerHTML = `<div class="ytmusic-static-lyrics">${this.escapeHtml(lyricsData.text)}</div>`;

                // Note: Static lyrics don't have timing data, so no click-to-seek functionality

                // Update source separately
                this.lyricsSourceContainer.textContent = sourceText;
            } else {
                console.log('❌ Unknown lyrics type:', lyricsData.type);
                this.showError('Unknown lyrics format received.');
            }
        } catch (error) {
            console.error('❌ Error displaying lyrics:', error);
            this.showError('Error displaying lyrics. Please try refreshing the page.');
        }
    }

    // Escape HTML to prevent XSS
    escapeHtml(text) {
        if (!text) { return ''; }
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Set up click-to-seek functionality for lyrics lines
     * @private
     */
    setupClickToSeek() {
        if (!this.lyricsContainer) return;

        const lyricsLines = this.lyricsContainer.querySelectorAll('.ytmusic-lyrics-line[data-time]');

        lyricsLines.forEach(line => {
            line.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const targetTime = parseFloat(line.dataset.time);
                if (!isNaN(targetTime)) {
                    // Apply both the early show offset and user's sync offset setting
                    const seekTime = (targetTime - CONTENT_CONFIG.TIMING.LYRICS_EARLY_SHOW) + 0.1 + (this.currentSettings.SYNC_OFFSET || 0);
                    this.seekToTime(Math.max(0, seekTime)); // Don't seek to negative time
                }
            });
        });

        console.log(`🎵 Click-to-seek enabled for ${lyricsLines.length} lyrics lines`);
    }


    /**
     * Seek to a specific time in the song and update lyrics highlight
     * @param {number} time - Time in seconds to seek to
     * @private
     */
    seekToTime(time) {
        try {
            console.log(`🎵 Seeking to time: ${time}s`);

            const videoElement = document.querySelector(CONTENT_CONFIG.SELECTORS.VIDEO_ELEMENT);
            if (videoElement) {
                videoElement.currentTime = time;
                console.log(`✅ Seeked to ${time}s using video element`);

                // Update lyrics highlight immediately after seeking, especially when paused
                setTimeout(() => {
                    const currentTime = this.getCurrentPlaybackTime();
                    if (currentTime !== null && this.lyricsData && this.lyricsData.type === 'synced') {
                        this.forceHighlightUpdate(currentTime);
                        console.log(`🎵 Updated lyrics highlight after seeking to ${currentTime.toFixed(1)}s`);
                    }
                }, CONTENT_CONFIG.TIMING.SEEK_HIGHLIGHT_DELAY);

                return true;
            }

            console.warn('❌ Could not find video element');
            return false;
        } catch (error) {
            console.error('❌ Error seeking to time:', error);
            return false;
        }
    }

    /**
     * Start lyrics synchronization (only runs when video is playing)
     * @private
     */
    startSynchronization() {
        if (!this.lyricsData || this.lyricsData.type !== 'synced') {
            console.log('❌ Cannot start sync: no synced lyrics data');
            return;
        }

        this.stopSynchronization();
        console.log('🎵 Starting synchronization with', this.lyricsData.lines.length, 'lines');

        this.syncInterval = setInterval(() => {
            // Only sync when video is playing
            if (!this.isVideoPlaying()) {
                return;
            }

            const currentTime = this.getCurrentPlaybackTime();
            if (currentTime !== null) {
                this.highlightCurrentLine(currentTime);
            }
        }, CONTENT_CONFIG.TIMING.SYNC_UPDATE_INTERVAL);
    }

    stopSynchronization() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
    }

    /**
     * Get current playback time from video element only
     * @returns {number|null} Current time in seconds or null if unavailable
     * @private
     */
    getCurrentPlaybackTime() {
        try {
            const videoElement = document.querySelector(CONTENT_CONFIG.SELECTORS.VIDEO_ELEMENT);
            if (videoElement && !isNaN(videoElement.currentTime)) {
                return videoElement.currentTime;
            }
            return null;
        } catch (error) {
            console.error('❌ Error getting playback time:', error);
            return null;
        }
    }

    /**
     * Check if video is currently playing
     * @returns {boolean} True if video is playing
     * @private
     */
    isVideoPlaying() {
        try {
            const videoElement = document.querySelector(CONTENT_CONFIG.SELECTORS.VIDEO_ELEMENT);
            if (videoElement) {
                return !videoElement.paused && !videoElement.ended;
            }
            return false;
        } catch (error) {
            console.error('❌ Error checking video play state:', error);
            return false;
        }
    }

    /**
     * Force highlight update regardless of play state
     * @param {number} currentTime - Current playback time in seconds
     * @private
     */
    forceHighlightUpdate(currentTime) {
        if (!this.lyricsContainer || !this.lyricsData || this.lyricsData.type !== 'synced') {
            return;
        }

        // Use the same highlighting logic but bypass play state checks
        this.highlightCurrentLine(currentTime);
    }

    /**
     * Highlight current lyrics line and auto-scroll
     * @param {number} currentTime - Current playback time in seconds
     * @private
     */
    highlightCurrentLine(currentTime) {
        if (!this.lyricsContainer) { return; }

        const lines = this.lyricsContainer.querySelectorAll(`.${CONTENT_CONFIG.CSS_CLASSES.LYRICS_LINE}`);
        let activeIndex = -1;

        // Simple timing calculation: current time + user sync offset
        const adjustedTime = currentTime + (this.currentSettings.SYNC_OFFSET || 0);

        // Find the current line - show lyrics LYRICS_EARLY_SHOW seconds before their timestamp
        for (let i = 0; i < lines.length; i++) {
            const lineTime = parseFloat(lines[i].dataset.time);

            // Show this line if current time is within LYRICS_EARLY_SHOW seconds of the line time
            if (adjustedTime >= (lineTime - CONTENT_CONFIG.TIMING.LYRICS_EARLY_SHOW)) {
                activeIndex = i;
            } else {
                break;
            }
        }

        // Remove previous highlighting
        lines.forEach((line, index) => {
            line.classList.remove('active', 'passed', 'upcoming');

            // Add appropriate class based on position relative to current line
            if (activeIndex >= 0) {
                if (index < activeIndex) {
                    line.classList.add('passed');
                } else if (index === activeIndex) {
                    line.classList.add('active');
                } else if (index === activeIndex + 1) {
                    line.classList.add('upcoming');
                }
            }
        });

        // Highlight current line and scroll
        if (activeIndex >= 0) {
            // Log current line for debugging (only when it changes)
            if (this.lastActiveIndex !== activeIndex) {
                const lineText = lines[activeIndex].textContent.trim();
                const lineTime = parseFloat(lines[activeIndex].dataset.time);
                console.log(`🎤 Current line [${lineTime}s]: "${lineText}" (playback: ${currentTime.toFixed(1)}s)`);
                this.lastActiveIndex = activeIndex;
            }

            // Auto-scroll to current line (now always centers)
            this.scrollToLine(lines[activeIndex]);
        } else if (currentTime > 0) {
            // Log when no line is active but music is playing
            if (this.lastActiveIndex !== -1) {
                console.log(`🎵 Waiting for first line (playback: ${currentTime.toFixed(1)}s)`);
                this.lastActiveIndex = -1;
            }
        }
    }

    /**
     * Scroll to the active lyrics line
     * @param {HTMLElement} element - The lyrics line element to scroll to
     * @private
     */
    scrollToLine(element) {
        if (!this.lyricsContainer) { return; }

        // Don't auto-scroll if user is manually scrolling
        if (this.isUserScrolling) {
            return;
        }

        // Don't auto-scroll if setting is disabled
        if (!this.currentSettings.AUTO_SCROLL) {
            return;
        }

        // Mark as programmatic scroll to prevent user scroll detection
        this.programmaticScroll = true;

        // Always center the active lyric in the container
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
            inline: 'nearest'
        });

        // Clear programmatic scroll flag after a short delay
        setTimeout(() => {
            this.programmaticScroll = false;
        }, CONTENT_CONFIG.TIMING.PROGRAMMATIC_SCROLL_DELAY);
    }

    showLoading() {
        try {
            if (!this.lyricsContainer) {
                this.setupLyricsOverlay();
                if (!this.lyricsContainer) {
                    console.log('❌ Cannot create lyrics container for loading display');
                    return;
                }
            }

            this.lyricsContainer.innerHTML = `
                <div class="ytmusic-lyrics-loading">
                    <div class="loading-icon">🎤</div>
                    <div class="loading-text">Searching for lyrics...</div>
                    <div class="loading-subtext">This may take a moment</div>
                </div>
            `;

            // Clear the source container when loading
            if (this.lyricsSourceContainer) {
                this.lyricsSourceContainer.textContent = '';
            }
        } catch (error) {
            console.error('❌ Error displaying loading message:', error);
        }
    }

    showError(message) {
        try {
            if (!this.lyricsContainer) {
                console.log('🎵 No lyrics container for error message, attempting to create...');
                this.setupLyricsOverlay();
                if (!this.lyricsContainer) {
                    console.log('❌ Cannot create lyrics container for error display');
                    return;
                }
            }

            // Ensure message is safe to display
            const safeMessage = this.escapeHtml(message || 'An unknown error occurred');

            this.lyricsContainer.innerHTML = `
                <div class="ytmusic-lyrics-error">
                    <div class="error-icon">❌</div>
                    <div class="error-message">${safeMessage}</div>
                </div>
            `;

            // Clear the source container when showing error
            if (this.lyricsSourceContainer) {
                this.lyricsSourceContainer.textContent = '';
            }

            console.log('🎵 Displayed error message:', message);
        } catch (error) {
            console.error('❌ Error displaying error message:', error);
            // Fallback: try to show a basic error message
            if (this.lyricsContainer) {
                this.lyricsContainer.innerHTML = '<div class="ytmusic-lyrics-error">❌ An error occurred</div>';
            }
        }
    }

    toggleLyrics() {
        this.isEnabled = !this.isEnabled;

        if (this.isEnabled) {
            this.startSynchronization();
        } else {
            this.stopSynchronization();
        }
    }    // Cleanup method for proper resource management
    cleanup() {
        this.stopSynchronization();

        // Clear user scroll timeout
        if (this.userScrollTimeout) {
            clearTimeout(this.userScrollTimeout);
            this.userScrollTimeout = null;
        }

        // Clear color picker debounce timeout
        if (this.colorPickerDebounceTimeout) {
            clearTimeout(this.colorPickerDebounceTimeout);
            this.colorPickerDebounceTimeout = null;
        }

        // Remove video event listeners
        this.removeVideoEventListeners();

        if (this.tabObserver) {
            this.tabObserver.disconnect();
            this.tabObserver = null;
        }

        // Stop section list enforcement
        this.stopSectionListEnforcement();

        if (this.lyricsContainer && this.lyricsContainer.parentNode) {
            this.lyricsContainer.parentNode.removeChild(this.lyricsContainer);
            this.lyricsContainer = null;
        }

        // Remove data attribute
        if (this.lyricsTabRenderer) {
            this.lyricsTabRenderer.removeAttribute('data-lyrics-active');
        }

        // Restore section list visibility
        this.manageSectionListVisibility(true);

        // Restore scroller class if needed
        if (this.lyricsTabRenderer && this.hadScrollerClass) {
            this.lyricsTabRenderer.classList.add('scroller');
        }

        // Close settings modal if open
        this.hideSettingsModal();

        console.log('🎵 Extension cleanup completed');
    }

    setupMessageListener() {
        try {
            chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
                try {
                    if (request.action === 'toggleLyrics') {
                        this.toggleLyrics();
                        sendResponse({ status: 'success' });
                    } else if (request.action === 'getCurrentSong') {
                        sendResponse(this.currentSong || { status: 'no_song' });
                    } else {
                        console.log('🎵 Unknown message action:', request.action);
                        sendResponse({ status: 'unknown_action' });
                    }
                } catch (error) {
                    console.error('❌ Error handling message:', error);
                    sendResponse({ status: 'error', message: error.message });
                }
                return true; // Keep message channel open
            });

            console.log('🎵 Message listener set up successfully');
        } catch (error) {
            console.error('❌ Failed to set up message listener:', error);
        }
    }

    // Force hide section list using multiple approaches
    forceHideSectionList() {
        const sectionList = document.querySelector('#tab-renderer > ytmusic-section-list-renderer');
        if (sectionList) {
            const pageType = sectionList.getAttribute('page-type');

            // Check page type before forcing hide
            if (pageType === 'MUSIC_PAGE_TYPE_TRACK_RELATED') {
                // For related tracks page, don't change anything
                console.log('🎵 Track related page detected - not forcing hide');
                return;
            }

            if (pageType !== 'MUSIC_PAGE_TYPE_TRACK_LYRICS') {
                // Only force hide for lyrics pages
                console.log('🎵 Not a track lyrics page - not forcing hide');
                return;
            }

            // Apply multiple hiding techniques
            sectionList.style.display = 'none';

            console.log('🎵 Force-hid section list');
        }
    }

    // Remove all extension-applied styling from section list
    removeSectionListStyling(sectionList) {
        if (sectionList) {
            // Batch style changes to avoid forced reflow
            requestAnimationFrame(() => {
                // Remove all styling properties that our extension might have applied
                const stylesToRemove = ['display', 'visibility', 'opacity', 'pointer-events', 'position', 'left'];
                stylesToRemove.forEach(prop => sectionList.style.removeProperty(prop));
                sectionList.removeAttribute('aria-hidden');

                console.log('🎵 Removed all extension styling from section list');
            });
        }
    }

    // Setup scroll event listeners for user scroll detection
    setupScrollListeners() {
        if (!this.lyricsContainer) { return; }

        this.lyricsContainer.addEventListener('scroll', () => {
            // Ignore programmatic scrolls from auto-scroll
            if (this.programmaticScroll) {
                return;
            }

            // User is actively scrolling
            this.isUserScrolling = true;

            // Clear existing timeout
            if (this.userScrollTimeout) {
                clearTimeout(this.userScrollTimeout);
            }

            // Set timeout to detect when user stops scrolling
            this.userScrollTimeout = setTimeout(() => {
                this.isUserScrolling = false;
                console.log('🎵 User stopped scrolling');
            }, 1000); // 1 second after user stops scrolling

            // Throttled logging to prevent spam
            const now = Date.now();
            if (now - this.lastScrollLog > 1000) { // Log at most once per second
                console.log('🎵 User is scrolling');
                this.lastScrollLog = now;
            }
        }, { passive: true });

        this.lyricsContainer.addEventListener('wheel', () => {
            // Ignore programmatic scrolls
            if (this.programmaticScroll) {
                return;
            }

            // User is scrolling with mouse wheel
            this.isUserScrolling = true;

            if (this.userScrollTimeout) {
                clearTimeout(this.userScrollTimeout);
            }

            this.userScrollTimeout = setTimeout(() => {
                this.isUserScrolling = false;
            }, 1000);
        }, { passive: true });

        this.lyricsContainer.addEventListener('touchstart', () => {
            // User is starting to touch scroll
            this.isUserScrolling = true;
        }, { passive: true });

        this.lyricsContainer.addEventListener('touchend', () => {
            // User finished touch scrolling
            if (this.userScrollTimeout) {
                clearTimeout(this.userScrollTimeout);
            }

            this.userScrollTimeout = setTimeout(() => {
                this.isUserScrolling = false;
            }, 1000);
        }, { passive: true });
    }


    // ============================================================================
    // SETTINGS SYSTEM
    // ============================================================================

    // Load settings for a specific song or global settings
    loadSettings(songInfo = null) {
        try {
            const songKey = songInfo ? this.getSongKey(songInfo) : null;

            // Load global settings first
            const globalSettings = JSON.parse(localStorage.getItem(CONTENT_CONFIG.SETTINGS.STORAGE_KEYS.GLOBAL) || '{}');

            // Start with defaults, then apply global settings
            let settings = { ...CONTENT_CONFIG.SETTINGS.DEFAULTS, ...globalSettings };

            // If song-specific settings exist, apply them
            if (songKey) {
                const songSettings = JSON.parse(localStorage.getItem(CONTENT_CONFIG.SETTINGS.STORAGE_KEYS.SONG_SPECIFIC) || '{}');
                if (songSettings[songKey]) {
                    settings = { ...settings, ...songSettings[songKey] };

                    // Show toast notification for loaded song settings
                    this.showToast(`${CONTENT_CONFIG.UI_TEXT.SETTINGS_LOADED}: "${songInfo.title}"`);
                }
            }

            this.currentSettings = settings;
            this.applySettings();

            console.log('🎵 Settings loaded:', settings);
            return settings;
        } catch (error) {
            console.error('❌ Error loading settings:', error);
            this.currentSettings = { ...CONTENT_CONFIG.SETTINGS.DEFAULTS };
            return this.currentSettings;
        }
    }

    // Save settings
    saveSettings(scope, settings) {
        try {
            if (scope === 'global') {
                localStorage.setItem(CONTENT_CONFIG.SETTINGS.STORAGE_KEYS.GLOBAL, JSON.stringify(settings));
                console.log('🎵 Global settings saved:', settings);
            } else if (scope === 'song' && this.currentSong) {
                const songKey = this.getSongKey(this.currentSong);
                const songSettings = JSON.parse(localStorage.getItem(CONTENT_CONFIG.SETTINGS.STORAGE_KEYS.SONG_SPECIFIC) || '{}');
                songSettings[songKey] = settings;
                localStorage.setItem(CONTENT_CONFIG.SETTINGS.STORAGE_KEYS.SONG_SPECIFIC, JSON.stringify(songSettings));
                console.log('🎵 Song-specific settings saved for:', this.currentSong.title, settings);
            }
        } catch (error) {
            console.error('❌ Error saving settings:', error);
        }
    }

    // Get unique key for a song
    getSongKey(songInfo) {
        return `${songInfo.artist}-${songInfo.title}`.toLowerCase().replace(/[^a-z0-9]/g, '');
    }

    // Apply current settings to the UI
    applySettings() {
        try {
            // Apply font size to lyrics lines specifically
            if (this.lyricsContainer) {
                this.lyricsContainer.style.fontSize = `${this.currentSettings.FONT_SIZE}px`;

                // Also apply to individual lyrics lines for more specific targeting
                const lyricsLines = this.lyricsContainer.querySelectorAll('.ytmusic-lyrics-line');
                lyricsLines.forEach(line => {
                    line.style.fontSize = `${this.currentSettings.FONT_SIZE}px`;
                });

                // Also apply to static lyrics if present
                const staticLyrics = this.lyricsContainer.querySelector('.ytmusic-static-lyrics');
                if (staticLyrics) {
                    staticLyrics.style.fontSize = `${this.currentSettings.FONT_SIZE}px`;
                }
            }

            // Apply full theme color system using CSS custom properties
            this.applyThemeColor(this.currentSettings.HIGHLIGHT_COLOR);

            // Auto-scroll setting is applied in the sync methods
            console.log('🎵 Settings applied to UI');
        } catch (error) {
            console.error('❌ Error applying settings:', error);
        }
    }

    // Apply theme color and generate all related color variants
    applyThemeColor(color) {
        try {
            // Parse the hex color to RGB
            const hex = color.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);

            // Create lighter and darker variants
            const lightR = Math.min(255, Math.floor(r + (255 - r) * 0.3));
            const lightG = Math.min(255, Math.floor(g + (255 - g) * 0.3));
            const lightB = Math.min(255, Math.floor(b + (255 - b) * 0.3));

            const darkR = Math.floor(r * 0.7);
            const darkG = Math.floor(g * 0.7);
            const darkB = Math.floor(b * 0.7);

            // Set all theme color CSS custom properties
            const root = document.documentElement;
            root.style.setProperty('--theme-color', color);
            root.style.setProperty('--theme-color-light', `rgb(${lightR}, ${lightG}, ${lightB})`);
            root.style.setProperty('--theme-color-dark', `rgb(${darkR}, ${darkG}, ${darkB})`);
            root.style.setProperty('--theme-color-alpha-10', `rgba(${r}, ${g}, ${b}, 0.1)`);
            root.style.setProperty('--theme-color-alpha-15', `rgba(${r}, ${g}, ${b}, 0.15)`);
            root.style.setProperty('--theme-color-alpha-20', `rgba(${r}, ${g}, ${b}, 0.2)`);
            root.style.setProperty('--theme-color-alpha-30', `rgba(${r}, ${g}, ${b}, 0.3)`);
            root.style.setProperty('--theme-color-alpha-40', `rgba(${r}, ${g}, ${b}, 0.4)`);
            root.style.setProperty('--theme-color-alpha-50', `rgba(${r}, ${g}, ${b}, 0.5)`);
            root.style.setProperty('--theme-color-glow', `rgba(${r}, ${g}, ${b}, 0.7)`);

            // Also set the lyrics highlight color for backwards compatibility
            root.style.setProperty('--lyrics-highlight-color', color);

            console.log('Theme color applied:', color);
        } catch (error) {
            console.error('❌ Error applying theme color:', error);
        }
    }

    // Create and show settings modal
    showSettingsModal() {
        try {
            // Remove existing modal if present
            this.hideSettingsModal();

            // Backup current settings so we can revert if user cancels
            this.originalSettings = { ...this.currentSettings };

            // Create modal structure
            const modal = document.createElement('div');
            modal.className = 'ytmusic-settings-modal';
            modal.innerHTML = `
                <div class="ytmusic-settings-content">
                    <div class="ytmusic-settings-header">
                        <div class="ytmusic-settings-title">
                            ⚙️ ${CONTENT_CONFIG.UI_TEXT.SETTINGS_TITLE}
                        </div>
                        <button class="ytmusic-settings-close">×</button>
                    </div>
                    <div class="ytmusic-settings-body">
                        <div class="ytmusic-settings-group">
                            <label class="ytmusic-settings-label">${CONTENT_CONFIG.UI_TEXT.SYNC_OFFSET_LABEL}</label>
                            <div class="ytmusic-settings-slider-group">
                                <input type="range" class="ytmusic-settings-slider" id="sync-offset-slider"
                                       min="${CONTENT_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.min}"
                                       max="${CONTENT_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.max}"
                                       step="${CONTENT_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.step}"
                                       value="${this.currentSettings.SYNC_OFFSET}">
                                <input type="number" class="ytmusic-settings-value" id="sync-offset-value"
                                       min="${CONTENT_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.min}"
                                       max="${CONTENT_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.max}"
                                       step="${CONTENT_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.step}"
                                       value="${this.currentSettings.SYNC_OFFSET}">
                            </div>
                        </div>
                        
                        <div class="ytmusic-settings-group">
                            <label class="ytmusic-settings-label">${CONTENT_CONFIG.UI_TEXT.FONT_SIZE_LABEL}</label>
                            <div class="ytmusic-settings-slider-group">
                                <input type="range" class="ytmusic-settings-slider" id="font-size-slider"
                                       min="${CONTENT_CONFIG.SETTINGS.LIMITS.FONT_SIZE.min}"
                                       max="${CONTENT_CONFIG.SETTINGS.LIMITS.FONT_SIZE.max}"
                                       step="${CONTENT_CONFIG.SETTINGS.LIMITS.FONT_SIZE.step}"
                                       value="${this.currentSettings.FONT_SIZE}">
                                <input type="number" class="ytmusic-settings-value" id="font-size-value"
                                       min="${CONTENT_CONFIG.SETTINGS.LIMITS.FONT_SIZE.min}"
                                       max="${CONTENT_CONFIG.SETTINGS.LIMITS.FONT_SIZE.max}"
                                       step="${CONTENT_CONFIG.SETTINGS.LIMITS.FONT_SIZE.step}"
                                       value="${this.currentSettings.FONT_SIZE}">
                            </div>
                        </div>
                        
                        <div class="ytmusic-settings-group">
                            <div class="ytmusic-settings-toggle">
                                <label class="ytmusic-settings-label">${CONTENT_CONFIG.UI_TEXT.AUTO_SCROLL_LABEL}</label>
                                <div class="ytmusic-toggle-switch ${this.currentSettings.AUTO_SCROLL ? 'active' : ''}" id="auto-scroll-toggle"></div>
                            </div>
                        </div>
                        
                        <div class="ytmusic-settings-group">
                            <label class="ytmusic-settings-label">${CONTENT_CONFIG.UI_TEXT.HIGHLIGHT_COLOR_LABEL}</label>
                            <div class="ytmusic-settings-color">
                                <input type="color" class="ytmusic-color-picker" id="highlight-color-picker"
                                       value="${this.currentSettings.HIGHLIGHT_COLOR}">
                                <input type="text" class="ytmusic-color-value" id="highlight-color-value"
                                       value="${this.currentSettings.HIGHLIGHT_COLOR}" maxlength="7">
                            </div>
                        </div>
                    </div>
                    <div class="ytmusic-settings-actions">
                        <button class="ytmusic-settings-btn primary" id="save-current-song">
                            ${CONTENT_CONFIG.UI_TEXT.SAVE_CURRENT_SONG}
                        </button>
                        <button class="ytmusic-settings-btn primary" id="save-all-songs">
                            ${CONTENT_CONFIG.UI_TEXT.SAVE_ALL_SONGS}
                        </button>
                        <button class="ytmusic-settings-btn" id="apply-temporary">
                            ${CONTENT_CONFIG.UI_TEXT.APPLY_TEMPORARY}
                        </button>
                        <button class="ytmusic-settings-btn secondary" id="reset-defaults">
                            ${CONTENT_CONFIG.UI_TEXT.RESET_DEFAULTS}
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            this.settingsModal = modal;

            // Make modal focusable for keyboard events
            modal.setAttribute('tabindex', '-1');

            // Use the exact same approach as fade-out but reversed
            // Start with fade-out state, then remove it to trigger fade-in
            modal.classList.add('fade-out');

            // Force reflow to ensure the fade-out class is applied
            modal.offsetHeight;

            // Remove fade-out class to trigger fade-in (same as fade-out but reversed)
            setTimeout(() => {
                modal.classList.remove('fade-out');
                modal.focus();
            }, 10);

            // Setup event listeners
            this.setupSettingsListeners();

            console.log('🎵 Settings modal opened');
        } catch (error) {
            console.error('❌ Error showing settings modal:', error);
        }
    }

    // Hide settings modal
    hideSettingsModal(saveChanges = false) {
        if (this.settingsModal) {
            // Clear any pending color picker debounce timeout
            if (this.colorPickerDebounceTimeout) {
                clearTimeout(this.colorPickerDebounceTimeout);
                this.colorPickerDebounceTimeout = null;
            }

            // If not saving changes, revert to original settings
            if (!saveChanges && this.originalSettings) {
                this.currentSettings = { ...this.originalSettings };
                this.applySettings();
            } else if (saveChanges) {
                // If saving changes, apply any pending color changes immediately
                this.applySettings();
            }

            // Add fade-out animation
            this.settingsModal.classList.add('fade-out');

            // Remove modal after animation completes
            setTimeout(() => {
                if (this.settingsModal && this.settingsModal.parentNode) {
                    this.settingsModal.remove();
                }
                this.settingsModal = null;
                this.originalSettings = null; // Clear backup
                console.log('🎵 Settings modal closed');
            }, 300);
        }
    }

    // Setup event listeners for settings modal
    setupSettingsListeners() {
        if (!this.settingsModal) { return; }

        const modal = this.settingsModal;

        // Close modal without saving
        modal.querySelector('.ytmusic-settings-close').addEventListener('click', () => {
            this.hideSettingsModal(false); // Don't save changes
        });

        // Close on background click without saving
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideSettingsModal(false); // Don't save changes
            }
        });

        // Close on Escape key
        modal.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideSettingsModal(false); // Don't save changes
            }
        });

        // Sync offset controls
        const syncSlider = modal.querySelector('#sync-offset-slider');
        const syncValue = modal.querySelector('#sync-offset-value');

        syncSlider.addEventListener('input', (e) => {
            syncValue.value = e.target.value;
            this.currentSettings.SYNC_OFFSET = parseFloat(e.target.value);
            this.applySettings();
        });

        syncValue.addEventListener('input', (e) => {
            const value = parseFloat(e.target.value);
            if (!isNaN(value)) {
                // Clamp value to defined limits
                const clampedValue = Math.max(
                    CONTENT_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.min,
                    Math.min(CONTENT_CONFIG.SETTINGS.LIMITS.SYNC_OFFSET.max, value)
                );

                // Update both inputs with clamped value
                syncSlider.value = clampedValue;
                if (clampedValue !== value) {
                    syncValue.value = clampedValue; // Update text input if value was clamped
                }

                this.currentSettings.SYNC_OFFSET = clampedValue;
                this.applySettings();
            }
        });

        // Font size controls
        const fontSlider = modal.querySelector('#font-size-slider');
        const fontValue = modal.querySelector('#font-size-value');

        fontSlider.addEventListener('input', (e) => {
            fontValue.value = e.target.value;
            this.currentSettings.FONT_SIZE = parseInt(e.target.value);
            this.applySettings();
        });

        fontValue.addEventListener('input', (e) => {
            const value = parseInt(e.target.value);
            if (!isNaN(value)) {
                // Clamp value to defined limits
                const clampedValue = Math.max(
                    CONTENT_CONFIG.SETTINGS.LIMITS.FONT_SIZE.min,
                    Math.min(CONTENT_CONFIG.SETTINGS.LIMITS.FONT_SIZE.max, value)
                );

                // Update both inputs with clamped value
                fontSlider.value = clampedValue;
                if (clampedValue !== value) {
                    fontValue.value = clampedValue; // Update text input if value was clamped
                }

                this.currentSettings.FONT_SIZE = clampedValue;
                this.applySettings();
            }
        });

        // Auto-scroll toggle
        const autoScrollToggle = modal.querySelector('#auto-scroll-toggle');
        autoScrollToggle.addEventListener('click', () => {
            this.currentSettings.AUTO_SCROLL = !this.currentSettings.AUTO_SCROLL;
            autoScrollToggle.classList.toggle('active', this.currentSettings.AUTO_SCROLL);
        });

        // Color picker controls with debounced updates
        const colorPicker = modal.querySelector('#highlight-color-picker');
        const colorValue = modal.querySelector('#highlight-color-value');

        // Helper function to debounce color updates
        const debouncedColorUpdate = (color) => {
            // Clear existing timeout
            if (this.colorPickerDebounceTimeout) {
                clearTimeout(this.colorPickerDebounceTimeout);
            }

            // Update the setting immediately for UI responsiveness
            this.currentSettings.HIGHLIGHT_COLOR = color;

            // Debounce the expensive applySettings call
            this.colorPickerDebounceTimeout = setTimeout(() => {
                this.applySettings();
                console.log('🎵 Applied debounced color change:', color);
            }, 150); // 150ms debounce delay
        };

        // Color picker input event (fires while dragging)
        colorPicker.addEventListener('input', (e) => {
            colorValue.value = e.target.value;
            debouncedColorUpdate(e.target.value);
        });

        // Color picker change event (fires when done picking)
        colorPicker.addEventListener('change', (e) => {
            // Clear debounce and apply immediately when user is done
            if (this.colorPickerDebounceTimeout) {
                clearTimeout(this.colorPickerDebounceTimeout);
                this.colorPickerDebounceTimeout = null;
            }

            colorValue.value = e.target.value;
            this.currentSettings.HIGHLIGHT_COLOR = e.target.value;
            this.applySettings();
            console.log('🎵 Applied final color change:', e.target.value);
        });

        // Text input for manual color entry
        colorValue.addEventListener('input', (e) => {
            const { value } = e.target;
            if (/^#[0-9A-F]{6}$/i.test(value)) {
                colorPicker.value = value;
                debouncedColorUpdate(value);
            }
        });

        // Text input blur event (when user clicks away)
        colorValue.addEventListener('blur', (e) => {
            const { value } = e.target;
            if (/^#[0-9A-F]{6}$/i.test(value)) {
                // Clear debounce and apply immediately when user clicks away
                if (this.colorPickerDebounceTimeout) {
                    clearTimeout(this.colorPickerDebounceTimeout);
                    this.colorPickerDebounceTimeout = null;
                }

                colorPicker.value = value;
                this.currentSettings.HIGHLIGHT_COLOR = value;
                this.applySettings();
                console.log('🎵 Applied final color change from text input:', value);
            }
        });

        // Action buttons
        modal.querySelector('#save-current-song').addEventListener('click', () => {
            if (this.currentSong) {
                this.saveSettings('song', this.currentSettings);
                this.showToast(`${CONTENT_CONFIG.UI_TEXT.SETTINGS_APPLIED} for "${this.currentSong.title}"`);
                this.hideSettingsModal(true); // Save changes
            }
        });

        modal.querySelector('#save-all-songs').addEventListener('click', () => {
            this.saveSettings('global', this.currentSettings);
            this.showToast(`${CONTENT_CONFIG.UI_TEXT.SETTINGS_APPLIED} globally`);
            this.hideSettingsModal(true); // Save changes
        });

        modal.querySelector('#apply-temporary').addEventListener('click', () => {
            this.showToast(`${CONTENT_CONFIG.UI_TEXT.SETTINGS_APPLIED} temporarily`);
            this.hideSettingsModal(true); // Save changes (keep temporary)
        });

        modal.querySelector('#reset-defaults').addEventListener('click', () => {
            this.currentSettings = { ...CONTENT_CONFIG.SETTINGS.DEFAULTS };
            this.applySettings();
            this.updateSettingsModal();
            this.showToast('Settings reset to defaults');
        });
    }

    // Update settings modal with current values
    updateSettingsModal() {
        if (!this.settingsModal) { return; }

        const modal = this.settingsModal;

        // Update sync offset
        modal.querySelector('#sync-offset-slider').value = this.currentSettings.SYNC_OFFSET;
        modal.querySelector('#sync-offset-value').value = this.currentSettings.SYNC_OFFSET;

        // Update font size
        modal.querySelector('#font-size-slider').value = this.currentSettings.FONT_SIZE;
        modal.querySelector('#font-size-value').value = this.currentSettings.FONT_SIZE;

        // Update auto-scroll toggle
        modal.querySelector('#auto-scroll-toggle').classList.toggle('active', this.currentSettings.AUTO_SCROLL);

        // Update color picker
        modal.querySelector('#highlight-color-picker').value = this.currentSettings.HIGHLIGHT_COLOR;
        modal.querySelector('#highlight-color-value').value = this.currentSettings.HIGHLIGHT_COLOR;
    }

    // Show toast notification
    showToast(message, duration = CONTENT_CONFIG.TIMING.TOAST_DURATION) {
        try {
            // Remove existing toast
            const existingToast = document.querySelector('.ytmusic-toast');
            if (existingToast) {
                existingToast.remove();
            }

            // Create new toast
            const toast = document.createElement('div');
            toast.className = 'ytmusic-toast';
            toast.textContent = message;

            document.body.appendChild(toast);

            // Auto-hide after duration
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.classList.add('slide-out');
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.remove();
                        }
                    }, 300);
                }
            }, duration);

            console.log('🎵 Toast shown:', message);
        } catch (error) {
            console.error('❌ Error showing toast:', error);
        }
    }
}

// ============================================================================
// INITIALIZATION AND CLEANUP
// ============================================================================

// Initialize extension when page loads
let extensionInstance = null;

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        extensionInstance = new YouTubeMusicLyricsExtension();
    });
} else {
    extensionInstance = new YouTubeMusicLyricsExtension();
}

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (extensionInstance) {
        extensionInstance.cleanup();
    }
});

// Cleanup on visibility change (when tab becomes hidden)
document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden' && extensionInstance) {
        // Pause some operations to reduce background resource usage
        if (extensionInstance.userScrollTimeout) {
            clearTimeout(extensionInstance.userScrollTimeout);
            extensionInstance.userScrollTimeout = null;
        }
    }
});
